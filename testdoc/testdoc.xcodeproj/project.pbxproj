// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		A8D0BB432DE6BDB30003BBF5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A8D0BB292DE6BDB20003BBF5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A8D0BB302DE6BDB20003BBF5;
			remoteInfo = testdoc;
		};
		A8D0BB4D2DE6BDB30003BBF5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A8D0BB292DE6BDB20003BBF5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A8D0BB302DE6BDB20003BBF5;
			remoteInfo = testdoc;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		A8D0BB312DE6BDB20003BBF5 /* testdoc.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = testdoc.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A8D0BB422DE6BDB30003BBF5 /* testdocTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = testdocTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A8D0BB4C2DE6BDB30003BBF5 /* testdocUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = testdocUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		A8D0BB542DE6BDB30003BBF5 /* Exceptions for "testdoc" folder in "testdoc" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = A8D0BB302DE6BDB20003BBF5 /* testdoc */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		A8D0BB332DE6BDB20003BBF5 /* testdoc */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				A8D0BB542DE6BDB30003BBF5 /* Exceptions for "testdoc" folder in "testdoc" target */,
			);
			path = testdoc;
			sourceTree = "<group>";
		};
		A8D0BB452DE6BDB30003BBF5 /* testdocTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testdocTests;
			sourceTree = "<group>";
		};
		A8D0BB4F2DE6BDB30003BBF5 /* testdocUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = testdocUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		A8D0BB2E2DE6BDB20003BBF5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A8D0BB3F2DE6BDB30003BBF5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A8D0BB492DE6BDB30003BBF5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A8D0BB282DE6BDB20003BBF5 = {
			isa = PBXGroup;
			children = (
				A8D0BB332DE6BDB20003BBF5 /* testdoc */,
				A8D0BB452DE6BDB30003BBF5 /* testdocTests */,
				A8D0BB4F2DE6BDB30003BBF5 /* testdocUITests */,
				A8D0BB322DE6BDB20003BBF5 /* Products */,
			);
			sourceTree = "<group>";
		};
		A8D0BB322DE6BDB20003BBF5 /* Products */ = {
			isa = PBXGroup;
			children = (
				A8D0BB312DE6BDB20003BBF5 /* testdoc.app */,
				A8D0BB422DE6BDB30003BBF5 /* testdocTests.xctest */,
				A8D0BB4C2DE6BDB30003BBF5 /* testdocUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A8D0BB302DE6BDB20003BBF5 /* testdoc */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A8D0BB552DE6BDB30003BBF5 /* Build configuration list for PBXNativeTarget "testdoc" */;
			buildPhases = (
				A8D0BB2D2DE6BDB20003BBF5 /* Sources */,
				A8D0BB2E2DE6BDB20003BBF5 /* Frameworks */,
				A8D0BB2F2DE6BDB20003BBF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				A8D0BB332DE6BDB20003BBF5 /* testdoc */,
			);
			name = testdoc;
			packageProductDependencies = (
			);
			productName = testdoc;
			productReference = A8D0BB312DE6BDB20003BBF5 /* testdoc.app */;
			productType = "com.apple.product-type.application";
		};
		A8D0BB412DE6BDB30003BBF5 /* testdocTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A8D0BB5A2DE6BDB30003BBF5 /* Build configuration list for PBXNativeTarget "testdocTests" */;
			buildPhases = (
				A8D0BB3E2DE6BDB30003BBF5 /* Sources */,
				A8D0BB3F2DE6BDB30003BBF5 /* Frameworks */,
				A8D0BB402DE6BDB30003BBF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A8D0BB442DE6BDB30003BBF5 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A8D0BB452DE6BDB30003BBF5 /* testdocTests */,
			);
			name = testdocTests;
			packageProductDependencies = (
			);
			productName = testdocTests;
			productReference = A8D0BB422DE6BDB30003BBF5 /* testdocTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		A8D0BB4B2DE6BDB30003BBF5 /* testdocUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A8D0BB5D2DE6BDB30003BBF5 /* Build configuration list for PBXNativeTarget "testdocUITests" */;
			buildPhases = (
				A8D0BB482DE6BDB30003BBF5 /* Sources */,
				A8D0BB492DE6BDB30003BBF5 /* Frameworks */,
				A8D0BB4A2DE6BDB30003BBF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A8D0BB4E2DE6BDB30003BBF5 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A8D0BB4F2DE6BDB30003BBF5 /* testdocUITests */,
			);
			name = testdocUITests;
			packageProductDependencies = (
			);
			productName = testdocUITests;
			productReference = A8D0BB4C2DE6BDB30003BBF5 /* testdocUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A8D0BB292DE6BDB20003BBF5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					A8D0BB302DE6BDB20003BBF5 = {
						CreatedOnToolsVersion = 16.3;
					};
					A8D0BB412DE6BDB30003BBF5 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = A8D0BB302DE6BDB20003BBF5;
					};
					A8D0BB4B2DE6BDB30003BBF5 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = A8D0BB302DE6BDB20003BBF5;
					};
				};
			};
			buildConfigurationList = A8D0BB2C2DE6BDB20003BBF5 /* Build configuration list for PBXProject "testdoc" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A8D0BB282DE6BDB20003BBF5;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = A8D0BB322DE6BDB20003BBF5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A8D0BB302DE6BDB20003BBF5 /* testdoc */,
				A8D0BB412DE6BDB30003BBF5 /* testdocTests */,
				A8D0BB4B2DE6BDB30003BBF5 /* testdocUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A8D0BB2F2DE6BDB20003BBF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A8D0BB402DE6BDB30003BBF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A8D0BB4A2DE6BDB30003BBF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A8D0BB2D2DE6BDB20003BBF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A8D0BB3E2DE6BDB30003BBF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A8D0BB482DE6BDB30003BBF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A8D0BB442DE6BDB30003BBF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A8D0BB302DE6BDB20003BBF5 /* testdoc */;
			targetProxy = A8D0BB432DE6BDB30003BBF5 /* PBXContainerItemProxy */;
		};
		A8D0BB4E2DE6BDB30003BBF5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A8D0BB302DE6BDB20003BBF5 /* testdoc */;
			targetProxy = A8D0BB4D2DE6BDB30003BBF5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A8D0BB562DE6BDB30003BBF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = testdoc/testdoc.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = testdoc/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				"INFOPLIST_KEY_UISupportsDocumentBrowser[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UISupportsDocumentBrowser[sdk=iphonesimulator*]" = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = DC.testdoc;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		A8D0BB572DE6BDB30003BBF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = testdoc/testdoc.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = testdoc/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				"INFOPLIST_KEY_UISupportsDocumentBrowser[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UISupportsDocumentBrowser[sdk=iphonesimulator*]" = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = DC.testdoc;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		A8D0BB582DE6BDB30003BBF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A8D0BB592DE6BDB30003BBF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		A8D0BB5B2DE6BDB30003BBF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = DC.testdocTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/testdoc.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/testdoc";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		A8D0BB5C2DE6BDB30003BBF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = DC.testdocTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/testdoc.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/testdoc";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		A8D0BB5E2DE6BDB30003BBF5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = DC.testdocUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = testdoc;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		A8D0BB5F2DE6BDB30003BBF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = MRJZ8HXW49;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = DC.testdocUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = testdoc;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A8D0BB2C2DE6BDB20003BBF5 /* Build configuration list for PBXProject "testdoc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8D0BB582DE6BDB30003BBF5 /* Debug */,
				A8D0BB592DE6BDB30003BBF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A8D0BB552DE6BDB30003BBF5 /* Build configuration list for PBXNativeTarget "testdoc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8D0BB562DE6BDB30003BBF5 /* Debug */,
				A8D0BB572DE6BDB30003BBF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A8D0BB5A2DE6BDB30003BBF5 /* Build configuration list for PBXNativeTarget "testdocTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8D0BB5B2DE6BDB30003BBF5 /* Debug */,
				A8D0BB5C2DE6BDB30003BBF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A8D0BB5D2DE6BDB30003BBF5 /* Build configuration list for PBXNativeTarget "testdocUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A8D0BB5E2DE6BDB30003BBF5 /* Debug */,
				A8D0BB5F2DE6BDB30003BBF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A8D0BB292DE6BDB20003BBF5 /* Project object */;
}
