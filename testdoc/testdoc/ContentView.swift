//
//  ContentView.swift
//  testdoc
//
//  Created by Ni<PERSON> on 28/05/25.
//

import SwiftUI
import AppKit
import Foundation

struct ContentView: View {
    @Binding var document: testdocDocument
    @State private var isEditing = false

    var body: some View {
        VStack(spacing: 0) {
            // Simple toolbar
            HStack {
                Text("Font Size:")
                Slider(value: Binding(
                    get: { document.fontSize },
                    set: { document.fontSize = $0 }
                ), in: 10...24, step: 1)
                .frame(width: 100)

                Text("\(Int(document.fontSize))pt")
                    .frame(width: 30)

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text("Words: \(document.metadata.wordCount)")
                        .font(.caption)
                    Text("Characters: \(document.metadata.characterCount)")
                        .font(.caption)
                }
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))

            // Main content area
            TextEditor(text: $document.text)
                .font(.system(size: document.fontSize, design: .default))
                .padding()
                .onChange(of: document.text) { _, _ in
                    isEditing = true
                    // Auto-save after a delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        isEditing = false
                    }
                }

            // Status bar
            HStack {
                Text("Document: \(document.metadata.title)")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                if isEditing {
                    Text("Editing")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(4)
                }

                Text("Last saved: \(document.metadata.lastModifiedDate, style: .relative)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            .padding(.vertical, 4)
            .background(Color(NSColor.controlBackgroundColor))
        }
        .navigationTitle(document.metadata.title)
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                Button("Export as Text") {
                    exportAsPlainText()
                }

                Button("Document Info") {
                    showDocumentInfo()
                }
            }
        }
    }

    private func exportAsPlainText() {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.plainText]
        panel.nameFieldStringValue = "\(document.metadata.title).txt"

        panel.begin { response in
            if response == .OK, let url = panel.url {
                try? document.text.write(to: url, atomically: true, encoding: .utf8)
            }
        }
    }

    private func showDocumentInfo() {
        let alert = NSAlert()
        alert.messageText = "Document Information"
        alert.informativeText = """
        Title: \(document.metadata.title)
        Created: \(document.metadata.creationDate.formatted())
        Modified: \(document.metadata.lastModifiedDate.formatted())
        Word Count: \(document.metadata.wordCount)
        Character Count: \(document.metadata.characterCount)
        Font Size: \(Int(document.fontSize))pt
        """
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
}

#Preview {
    ContentView(document: Binding.constant(testdocDocument()))
        .frame(width: 800, height: 600)
}
