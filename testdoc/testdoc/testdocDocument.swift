//
//  testdocDocument.swift
//  testdoc
//
//  Created by <PERSON><PERSON> on 28/05/25.
//

import SwiftUI
import UniformTypeIdentifiers
import Foundation

extension UTType {
    static var noteDocument: UTType {
        UTType(importedAs: "com.testdoc.note")
    }
}

struct DocumentMetadata: Codable {
    var creationDate: Date
    var lastModifiedDate: Date
    var title: String
    var wordCount: Int
    var characterCount: Int

    init(title: String = "Untitled Document") {
        self.creationDate = Date()
        self.lastModifiedDate = Date()
        self.title = title
        self.wordCount = 0
        self.characterCount = 0
    }

    mutating func updateCounts(from text: String) {
        self.characterCount = text.count
        self.wordCount = text.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }.count
        self.lastModifiedDate = Date()
    }
}

struct DocumentData: Codable {
    var text: String
    var metadata: DocumentMetadata
    var fontSize: Double

    init(text: String = "Welcome to your new document!\n\nThis is a document-based Swift app that can:\n• Save and load documents\n• Track document metadata\n• Provide text editing capabilities\n• Show document statistics\n\nStart typing to create your content...",
         metadata: DocumentMetadata = DocumentMetadata(),
         fontSize: Double = 14) {
        self.text = text
        self.metadata = metadata
        self.fontSize = fontSize
    }
}

struct testdocDocument: FileDocument {
    var text: String {
        get { documentData.text }
        set {
            documentData.text = newValue
            documentData.metadata.updateCounts(from: newValue)
        }
    }

    var metadata: DocumentMetadata {
        get { documentData.metadata }
        set { documentData.metadata = newValue }
    }

    var fontSize: Double {
        get { documentData.fontSize }
        set { documentData.fontSize = newValue }
    }

    private var documentData: DocumentData

    init(text: String = "", metadata: DocumentMetadata = DocumentMetadata(), fontSize: Double = 14) {
        self.documentData = DocumentData(text: text, metadata: metadata, fontSize: fontSize)
        self.documentData.metadata.updateCounts(from: text)
    }

    static var readableContentTypes: [UTType] { [.noteDocument, .plainText] }

    init(configuration: ReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents else {
            throw CocoaError(.fileReadCorruptFile)
        }

        // Try to decode as JSON first (our custom format)
        if let documentData = try? JSONDecoder().decode(DocumentData.self, from: data) {
            self.documentData = documentData
        }
        // Fallback to plain text
        else if let string = String(data: data, encoding: .utf8) {
            self.documentData = DocumentData(text: string)
            self.documentData.metadata.updateCounts(from: string)
        }
        else {
            throw CocoaError(.fileReadCorruptFile)
        }
    }

    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted

        let data = try encoder.encode(documentData)
        return FileWrapper(regularFileWithContents: data)
    }
}
